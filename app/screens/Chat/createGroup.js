import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Platform,
  ScrollView,
  KeyboardAvoidingView,
  SafeAreaView,
  Modal,
} from "react-native";
import styles from "./styles";
import { isEmpty, isNull } from "lodash-es";
import { BaseColors } from "@config/theme";
import CSearch from "@components/CSearch";
import { CustomIcon } from "@config/LoadIcons";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import CHeader from "@components/CHeader";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import {
  createGroup,
  getChatSearchData,
  natureOfBusinessData,
} from "./apiCallFunction";
import SocketActions from "@redux/reducers/socket/actions";
import { Image } from "react-native";
import CInput from "@components/TextInput";
import CMultiDropdown from "@components/CDropDown/CMultiDropdown";
import RadioButton from "@components/CRadioButton";
import CButton from "@components/CButton";
import { groupUserList } from "@config/staticData";
import { FontFamily } from "@config/typography";
import CDropdown from "@components/CDropDown";
import { translate } from "../../lang/Translate";
import FastImage from "react-native-fast-image";
import AlreadyHaveStoryModal from "@components/AlreadyHaveStoryModal";
import { pickDocument } from "@screens/SingUpWithCompanyDetail/apiFunctions";
import CCamera from "@components/CameraButton/CCamera";
import { getUserFollowingList } from "@app/utils/commonFunction";

const { getChatList, setTotalMsgCount, setSelectedRoom } = SocketActions;

/**
 * CreateGroupChat Component
 *
 * This component handles the creation of group chats with the following features:
 * - Group profile picture selection
 * - Group name input
 * - Country selection via multi-dropdown
 * - Group type selection (Private/Public/Auto Join)
 * - Member selection and management
 * - Search functionality for finding users
 *
 * @param {Object} navigation - React Navigation object for screen navigation
 */
const CreateGroupChat = ({ navigation, route }) => {
  console.log("🚀 ~ CreateGroupChat ~ route:", route);
  const dispatch = useDispatch();

  // ==================== STATE MANAGEMENT ====================

  // Modal and UI states
  const [visible, setModalVisible] = useState(false); // Controls member selection modal visibility
  const [blockScreen, setBlockScreen] = useState(false); // Prevents certain actions when screen is blocked
  const [loading, setLoading] = useState(false); // Loading state for search operations
  const [profilePictureModal, setProfilePictureModal] = useState(false);
  const [profileImage, setProfileImage] = useState({});
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [imagePath, setImagePath] = useState({});

  // Search functionality states
  const [typingTimeout, setTypingTimeout] = useState(null); // Debounce timer for search input
  const [searchBtnData, setSearchBtnData] = useState(null); // Current search query
  const [searchChatList, setSearchChatList] = useState([]); // Search results for users

  // Group configuration states
  const [selectedOption, setSelectedOption] = useState({
    id: 1,
    options: "Private Group",
    key: "private",
  }); // Selected group type (1=Private, 2=Public, 3=Auto Join)
  const [userList, setUserList] = useState(groupUserList); // Complete list of available users
  const [addedMembers, setAddedMembers] = useState([]); // List of users added to the group
  const [natureOfBusinessList, setNatureOfBusinessList] = useState([]);
  const [selectnob, setSelectnob] = useState([]);
  const [grupName, setGrupName] = useState(route?.params?.groupName || "");

  // Add these state variables for errors
  const [errors, setErrors] = useState({
    profileImage: false,
    groupName: false,
    natureOfBusiness: false,
    members: false,
  });

  // ==================== REDUX STATE ====================

  // Socket-related states from Redux
  const {
    chatRooms,
    chatLoader,
    chatListNextEnablePage,
    chatData,
    typingData,
    bottomLoader,
  } = useSelector((s) => s.socket);

  // Authentication and subscription states
  const { isCurrentPlan, activePlanData, userData } = useSelector(
    (a) => a.auth
  );

  // ==================== DAYJS CONFIGURATION ====================
  dayjs.extend(utc);

  // ==================== SEARCH FUNCTIONALITY ====================

  /**
   * Handles search input with debouncing
   * Delays API call by 1 second after user stops typing
   * Only executes if user has valid subscription plan
   *
   * @param {string} searchBtnData - The search query entered by user
   */
  const handleInputChange = useCallback(
    (searchBtnData) => {
      clearTimeout(typingTimeout);
      setTypingTimeout(
        setTimeout(async () => {
          // Check if user has valid subscription before allowing search
          if (
            !isEmpty(isCurrentPlan) ||
            activePlanData?.is_prime_user ||
            activePlanData?.is_free_user
          ) {
            handleSearchChatList(searchBtnData, 1);
          }
        }, 1000)
      );
    },
    [typingTimeout, searchBtnData]
  );

  // ==================== FOCUS EFFECTS ====================

  const fetchNatureOfBusinessData = async () => {
    const resp = await natureOfBusinessData();
    setNatureOfBusinessList(resp);
  };

  const getUserShareList = useCallback(async () => {
    const resp = await getUserFollowingList(userData?.user_id);
    console.log("🚀 ~ CreateGroupChat ~ resp:", resp);
    const updatedData = resp?.data?.data.map((item) => ({
      ...item,
      status:
        addedMembers &&
        addedMembers.some((member) => member.user_id === item.user_id)
          ? "Remove"
          : "Add",
    }));
    setUserList(updatedData);
  }, [userData]);

  /**
   * Reset component state when screen comes into focus
   * Clears search data and resets message counts
   */
  useFocusEffect(
    useCallback(() => {
      setBlockScreen(false);
      setSearchBtnData(null);
      setSearchChatList([]);
      dispatch(setTotalMsgCount(0));
      dispatch(setSelectedRoom({}));
      fetchNatureOfBusinessData();
      getUserShareList();
    }, [])
  );

  /**
   * Initialize chat list when screen is not blocked
   */
  useFocusEffect(
    useCallback(() => {
      if (!blockScreen) {
        dispatch(getChatList("init", 1));
      }
    }, [blockScreen])
  );

  // ==================== SIDE EFFECTS ====================

  /**
   * Trigger search when searchBtnData changes
   * Only if user has valid subscription
   */
  useEffect(() => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      if (!isNull(searchBtnData)) {
        setLoading(true);
        handleInputChange(searchBtnData);
      }
    }
  }, [searchBtnData]);

  /**
   * Refresh chat list when new chat data is available
   */
  useEffect(() => {
    if (!isEmpty(chatData)) {
      dispatch(getChatList("init", 1));
    }
  }, [chatData]);

  // ==================== API FUNCTIONS ====================

  /**
   * Handles user search API call
   *
   * @param {string} searchBtnData - Search query (trimmed)
   * @param {number} page - Page number for pagination (default: 1)
   */
  const handleSearchChatList = async (searchBtnData, page = 1) => {
    console.log("🚀 ~ handleSearchChatList ~ searchBtnData:", searchBtnData);
    const resp = await getChatSearchData(searchBtnData?.trim(), page);
    console.log("🚀 ~ handleSearchChatList ~ resp:", resp?.data?.data);
    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      // const updatedData = resp?.data?.data.map((item) => ({
      //   ...item,
      //   status:
      //     addedMembers &&
      //     addedMembers.some((member) => member.user_id === item.user_id)
      //       ? "Remove"
      //       : "Add",
      // }));
      // console.log("🚀 ~ handleSearchChatList ~ updatedData:", updatedData);
      setSearchChatList({
        page: page,
        next_enable: resp?.data?.hasNextPage,
        data:
          page > 1
            ? [...searchChatList?.data, ...resp?.data.data] // Append for pagination
            : resp?.data?.data, // Replace for new search
      });
      setLoading(false);
    } else {
      setSearchChatList([]);
      setLoading(false);
    }
    setLoading(false);
  };

  // ==================== STATIC DATA ====================

  /**
   * Group type options
   */
  const groupType = [
    {
      id: 1,
      options: "Private Group",
      key: "private",
    },
    {
      id: 2,
      options: "Public Group",
      key: "public",
    },
    {
      id: 3,
      options: "Auto Join in Group",
      key: "auto_join",
    },
  ];

  // ==================== MEMBER MANAGEMENT ====================

  /**
   * Toggles member status between "Add" and "Remove"
   * Updates both userList and addedMembers states
   *
   * @param {number} userId - ID of the user to toggle
   */
  const handleToggleMember = (userId) => {
    setUserList((prevList) => {
      const updatedList = prevList.map((user) =>
        user.user_id === userId
          ? { ...user, status: user.status === "Add" ? "Remove" : "Add" }
          : user
      );
      const updatedAddedMembers = updatedList.filter(
        (user) => user.status === "Remove"
      );
      setAddedMembers(updatedAddedMembers);
      return updatedList;
    });
  };

  /**
   * Removes a member from the group (called from horizontal list X button)
   *
   * @param {Object} item - User object to remove
   */
  const handleRemoveMember = (item) => {
    handleToggleMember(item.user_id);
  };

  // ==================== VALIDATION FUNCTION ====================

  const validateGroupData = () => {
    let isValid = true;
    const newErrors = {
      profileImage: false,
      groupName: false,
      natureOfBusiness: false,
      members: false,
    };

    if (isEmpty(profileImage)) {
      newErrors.profileImage = true;
      isValid = false;
    }

    if (isEmpty(grupName?.trim())) {
      newErrors.groupName = true;
      isValid = false;
    }

    if (isEmpty(selectnob)) {
      newErrors.natureOfBusiness = true;
      isValid = false;
    }

    if (isEmpty(addedMembers)) {
      newErrors.members = true;
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // ==================== RENDER FUNCTIONS ====================

  /**
   * Renders individual member card in horizontal FlatList
   * Shows user image, name, and remove button
   *
   * @param {Object} item - User object
   * @param {number} index - Array index
   */
  const renderItem = ({ item, index }) => {
    return (
      <View style={{ marginHorizontal: 5 }}>
        {/* User Profile Image */}
        <Image
          source={{ uri: item.user_dp }}
          style={{ width: 60, height: 60, borderRadius: 25 }}
        />

        {/* User Name */}
        <Text
          style={{
            fontSize: 14,
            fontFamily: FontFamily.RobotoRegular,
          }}
          numberOfLines={1}
        >
          {item.full_name}
        </Text>

        {/* Remove Button */}
        <TouchableOpacity
          style={styles.removeBtn}
          activeOpacity={0.8}
          onPress={() => handleRemoveMember(item)}
        >
          <CustomIcon name="BsX" size={15} color={"#9DB2CE"} />
        </TouchableOpacity>
      </View>
    );
  };

  const HandleSelectImage = async (type) => {
    const data = await pickDocument(type, imagePath);
    setProfilePictureModal(false);
    // setIsLoading(false);
    setProfileImage(data);
    // setValue("companyProfile", data);
  };

  useEffect(() => {
    if (isCameraOpen) {
      setProfilePictureModal(false);
    }
    if (!isCameraOpen && imagePath?.uri) {
      setProfilePictureModal(false);
      HandleSelectImage("captureImg");
    }
  }, [isCameraOpen]);

  const renderUserList = ({ item, index }) => {
    console.log("🚀 ~ renderUserList ~ item:", item);
    return (
      <View
        key={item.id}
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginBottom: 10,
        }}
      >
        {/* User Profile Image */}
        <Image
          source={{ uri: item.user_dp }}
          style={{ width: 50, height: 50, borderRadius: 25 }}
        />

        {/* User Info */}
        <View style={{ marginLeft: 10 }}>
          <Text
            style={{
              fontSize: 18,
              fontFamily: FontFamily.RobotoMedium,
              marginBottom: 5,
            }}
          >
            {item.full_name}
          </Text>
        </View>

        {/* Add/Remove Button */}
        <View
          style={{
            flex: 1,
            alignItems: "flex-end",
          }}
        >
          <TouchableOpacity
            style={{
              paddingHorizontal: 10,
              paddingVertical: 6,
              borderWidth: 1,
              borderRadius: 5,
              borderColor: BaseColors.activeTab,
              backgroundColor:
                item.status === "Remove"
                  ? BaseColors.white
                  : BaseColors.activeTab,
            }}
            onPress={() => handleToggleMember(item.user_id)}
          >
            <Text
              style={{
                color:
                  item.status === "Remove"
                    ? BaseColors.activeTab
                    : BaseColors.white,
              }}
            >
              {item.status}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const createGrop = () => {
    // First validate all required fields
    if (!validateGroupData()) {
      return;
    }

    const member_ids = addedMembers.map((item) => item.user_id);
    const data = {
      group_name: grupName || "",
      image: profileImage,
      type: selectedOption?.key || "private",
      nature_business_id: JSON.stringify(selectnob),
      member: JSON.stringify(member_ids),
    };

    // Call API function only if validation passes
    createGroup(data);
  };

  // ==================== MAIN RENDER ====================
  return (
    <SafeAreaView style={styles.main}>
      {isCameraOpen ? (
        <View style={{ flex: 1 }}>
          <CCamera
            setImagePath={setImagePath}
            isCameraOpen={isCameraOpen}
            setIsCameraOpen={setIsCameraOpen}
            recordVideo={false}
          />
        </View>
      ) : (
        <>
          <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
            {/* Header */}
            <CHeader
              headingTitle={"Create Groups"}
              handleBackButton={() => navigation.goBack()}
            />

            {/* Group Profile Picture Section */}
            <TouchableOpacity
              style={styles.profileImgMainViewStyle}
              activeOpacity={0.8}
              onPress={() => setProfilePictureModal(true)}
            >
              {isEmpty(profileImage) ? (
                <View
                  style={[
                    styles.profileImgMainView,
                    { backgroundColor: "#C4C4C4" },
                  ]}
                >
                  <CustomIcon name="Camera" size={35} color={"#8E8383"} />
                </View>
              ) : (
                <View style={styles.profileImgMainView}>
                  <FastImage
                    source={{
                      uri:
                        Platform.OS === "ios"
                          ? `file://${profileImage?.uri?.replace("file://", "")}`
                          : profileImage?.uri,
                      priority: FastImage.priority.normal,
                    }}
                    style={styles.profileImgStyle}
                    resizeMode={FastImage.resizeMode.cover}
                  />
                </View>
              )}
              <TouchableOpacity
                style={[styles.editMainViewStyle]}
                activeOpacity={0.9}
                onPress={() => setProfilePictureModal(true)}
              >
                <CustomIcon
                  name={"Edit-Square"}
                  size={25}
                  color={BaseColors.activeTab}
                />
              </TouchableOpacity>
            </TouchableOpacity>
            {errors.profileImage && (
              <Text style={{ color: "red", marginLeft: 20 }}>
                Please select a group profile picture
              </Text>
            )}

            {/* Group Name Input */}
            <View style={styles.moreAboutTextInputStyle}>
              <CInput
                placeholderText={"Type Group Name"}
                containerStyle={styles.textInputStyle}
                onChange={(e) => {
                  setGrupName(e);
                }}
                value={grupName}
                isErrorMsg={errors?.groupName ? "Please enter group name" : ""}
              />
            </View>

            {/* Country Selection Dropdown */}
            <View style={[styles.moreAboutTextInputStyle]}>
              <CDropdown
                labelplaceholder={translate("Nature of business")}
                data={natureOfBusinessList}
                type="multi"
                labelField="label"
                valueField="value"
                setSelected={(e) => setSelectnob(e)}
                value={selectnob}
                isErrorMsg={
                  errors.natureOfBusiness
                    ? "Please select nature of business"
                    : ""
                }
              />
            </View>

            {/* Group Type Selection */}
            <View style={[styles.moreAboutTextInputStyle]}>
              <RadioButton
                type="options"
                options={groupType}
                defaultValue={selectedOption?.id}
                selectedOption1={(e) => {
                  setSelectedOption(e);
                }}
              />
            </View>

            {/* Add Member Button */}
            {isEmpty(addedMembers) ? (
              <View style={styles.moreAboutTextInputStyle}>
                <Text
                  style={styles.addText}
                  onPress={() => {
                    setModalVisible(true);
                    getUserShareList();
                  }}
                >
                  Add Member
                </Text>
              </View>
            ) : (
              <View
                style={{
                  borderWidth: 0.6,
                  borderRadius: 5,
                  borderColor: "#8E8383",
                  paddingHorizontal: 10,
                  paddingVertical: 15,
                  marginBottom: 20,
                  marginHorizontal: 20,
                }}
              >
                <FlatList
                  data={addedMembers}
                  renderItem={renderItem}
                  horizontal={true}
                  keyExtractor={(item, index) => item.user_id}
                  showsHorizontalScrollIndicator={false}
                />
              </View>
            )}
            {errors.members && (
              <Text style={{ color: "red", marginLeft: 20 }}>
                Please add at least one member
              </Text>
            )}
          </KeyboardAvoidingView>

          {/* Create Group Button */}
          <CButton style={styles.buttonView} onBtnClick={() => createGrop()}>
            Create Group
          </CButton>

          {/* ==================== MEMBER SELECTION MODAL ==================== */}
          <Modal
            animationType="slide"
            transparent={true}
            animationInTiming={5000}
            animationOutTiming={5000}
            visible={visible}
            onRequestClose={() => {
              setModalVisible(!visible);
            }}
          >
            <View style={styles.ovarlayStyle}>
              <View style={styles.modalView}>
                {/* Modal Header */}
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Text style={styles.modalTitleText}>{"Add Members"}</Text>

                  {/* Close Button */}
                  <TouchableOpacity
                    style={{
                      borderWidth: 1,
                      height: 24,
                      width: 24,
                      borderRadius: 5,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    activeOpacity={0.8}
                    onPress={() => setModalVisible(false)}
                  >
                    <CustomIcon name="BsX" size={20} color={BaseColors.black} />
                  </TouchableOpacity>
                </View>

                {/* Search Component */}
                <View
                  style={[
                    styles.searchView,
                    { marginHorizontal: 0, marginBottom: 20 },
                  ]}
                >
                  <CSearch
                    searchBtnData={searchBtnData}
                    setSearchBtnData={setSearchBtnData}
                  />
                </View>

                {/* Selected Members Horizontal List */}
                <View
                  style={{
                    borderWidth: 0.6,
                    borderRadius: 5,
                    borderColor: "#8E8383",
                    paddingHorizontal: 10,
                    paddingVertical: 15,
                    marginBottom: 20,
                  }}
                >
                  <FlatList
                    data={addedMembers}
                    renderItem={renderItem}
                    horizontal={true}
                    keyExtractor={(item, index) => item.user_id}
                    showsHorizontalScrollIndicator={false}
                  />
                </View>

                {/* Available Users Vertical List */}
                <ScrollView showsVerticalScrollIndicator={false}>
                  <FlatList
                    data={
                      !isEmpty(searchBtnData)
                        ? !isEmpty(searchChatList?.data) &&
                          !isNull(searchBtnData)
                          ? searchChatList?.data
                          : userList
                        : userList
                    }
                    renderItem={renderUserList}
                  />
                </ScrollView>
              </View>
            </View>
          </Modal>

          {profilePictureModal ? (
            <AlreadyHaveStoryModal
              visible={profilePictureModal}
              setModalVisible={(e) => setProfilePictureModal(e)}
              title1="captureFromCamera"
              title2="chooseFromGallery"
              onPressTitle1={() => setIsCameraOpen(true)}
              onPressTitle2={() => HandleSelectImage()}
            />
          ) : null}
        </>
      )}
    </SafeAreaView>
  );
};

export default CreateGroupChat;
